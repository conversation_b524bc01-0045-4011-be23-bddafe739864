# Importing pandas and matplotlib
import pandas as pd
import matplotlib.pyplot as plt

# Read in the Netflix CSV as a DataFrame
netflix_df = pd.read_csv("C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/Working Folder/Master in Sweden/Python/Datacamp Project/Investigating Netflix Movies/data/raw/netflix_data.csv")

# Start coding here! Use as many cells as you like
netflix_df.info()

df = netflix_df[(netflix_df['release_year'] >= 1990) & (netflix_df['release_year'] < 2000)]
df.head()

df_movie = df[df['type'] == 'Movie']
df_movie.head()

plt.hist(df_movie["duration"])
plt.title('Distribution of Movie Durations in the 1990s')
plt.xlabel('Duration (minutes)')
plt.ylabel('Number of Movies')
plt.show()

duration = 100
print(duration)

df_action = df_movie[df_movie['genre'] == 'Action']
df_action.head()

short_movie_count = 0
for index, row in df_action.iterrows():
    if row['duration'] < 90:
        short_movie_count += 1
print(short_movie_count)